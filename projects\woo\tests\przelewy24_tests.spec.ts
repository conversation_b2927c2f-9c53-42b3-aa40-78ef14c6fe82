import { test } from '@woo/fixtures/BaseTest';
import { PaymentMethods } from '@/utils/paymentMethods';
import wooPlugin from '@woo/WooPlugin';
import { BuckarooSettingsApiService } from '@woo/services/api';
import { OrderSteps } from '@woo/services/storefront';

test.describe('Przelewy24 Tests', () => {
    test('Place order with Przelewy24 aaaa', async ({ orderService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.PRZELEWY24,
            shippingMethod: 'flat',
        });
    });

    test('Place order with <PERSON>rzelewy24 (as guest)', async ({ orderService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.PRZELEWY24,
            disabledSteps: [OrderSteps.StorefrontLogin],
            shippingMethod: 'free',
        });
    });

    test('Place order with Przelewy24 (with multiple products)', async ({ orderService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.PRZELEWY24,
            shippingMethod: 'flat',
            products: [
                {
                    quantity: 2,
                    id: wooPlugin.getSelector('productValues.id.polo'),
                },
                {
                    quantity: 2,
                    id: wooPlugin.getSelector('productValues.id.tShortWithLogo'),
                },
            ],
        });
    });

    test('Refund order with Przelewy24 (from Admin)', async ({ orderService, refundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.PRZELEWY24,
            shippingMethod: 'free',
        });
        await refundService.executeSteps();
    });

    test('Refund order with Przelewy24 (from Plaza)', async ({ orderService, plazaRefundService }) => {
        await orderService.executeSteps({
            paymentMethod: PaymentMethods.PRZELEWY24,
            shippingMethod: 'flat',
        });
        await plazaRefundService.executeSteps();
    });

    test('Place order with Przelewy24 (with extra fixed fee)', async ({ orderService }) => {
        await new BuckarooSettingsApiService().updateSettings('buckaroo_p24', {
            extrachargeamount: '15',
        });

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.PRZELEWY24,
            shippingMethod: 'flat',
            paymentGatewayOptions: { paymentFee: 15 },
        });
    });

    test('Place order with Przelewy24 (with extra percentage fee)', async ({ orderService }) => {
        await new BuckarooSettingsApiService().updateSettings('buckaroo_p24', {
            extrachargeamount: '15%',
        });

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.PRZELEWY24,
            shippingMethod: 'free',
            paymentGatewayOptions: { paymentFee: '15%' },
        });
    });

    test('Place order with Przelewy24 (Failed)', async ({ orderService }) => {
        wooPlugin.testMode = 'negative';

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.PRZELEWY24,
            paymentGatewayOptions: { responseStatus: '490' },
        });
    });

    test('Place order with Przelewy24 (Rejected)', async ({ orderService }) => {
        wooPlugin.testMode = 'negative';

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.PRZELEWY24,
            paymentGatewayOptions: { responseStatus: '690' },
        });
    });

    test('Place order with Przelewy24 (Canceled)', async ({ orderService }) => {
        wooPlugin.testMode = 'negative';

        await orderService.executeSteps({
            paymentMethod: PaymentMethods.PRZELEWY24,
            shippingMethod: 'flat',
            paymentGatewayOptions: { responseStatus: '890' },
        });
    });
});
