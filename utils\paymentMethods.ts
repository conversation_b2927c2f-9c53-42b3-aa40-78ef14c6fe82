export const PaymentMethods = {
    ALIPAY: 'alipay',
    BANCONTACT: 'bancontactmrcash',
    PAYPAL: 'paypal',
    IDEAL: 'ideal',
    KBC: 'kbc',
    BANK_TRANSFER: 'transfer',
    BELFIUS: 'belfius',
    BILLINK: 'billink',
    EPS: 'eps',
    IN3: 'in3', // For magento is 'capayablein3', for Woo is 'in3'  -  Temp
    KLARNA_AUTHCAPT: 'klarnakp',
    KLARNA: 'klarna',
    KLARNA_SLICEIT: 'klarnain',
    MBWAY: 'mbway',
    MULTIBANCO: 'multibanco',
    PAYBYBANK: 'paybybank',
    RIVERTY: 'afterpay20',
    WECHATPAY: 'wechatpay',
    TRUSTLY: 'trustly',
    PRZELEWY24: 'przelewy24', // For magento is 'p24', for Woo is 'przelewy24' - Temp
    BLIK: 'blik',
    KNAKEN: 'knaken',
    CREDITCARD_DEBITCARD: 'creditcard',
    GIFTCARDS: 'giftcards',
    BOEKENBON: 'boekenbon',
    VVVGIFTCARD: 'vvvgiftcard',
    FASHION_CHEQUE: 'fashioncheque',
    SEPA: 'sepadirectdebit',
    PAYPEREMAIL: 'payperemail',
} as const;

export type PaymentMethodKey = keyof typeof PaymentMethods;

export type PaymentMethodCode = (typeof PaymentMethods)[keyof typeof PaymentMethods];
