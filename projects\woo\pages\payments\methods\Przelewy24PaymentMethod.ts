import { PaymentMethod } from '@woo/services/payments';
import { OrderSteps } from '@woo/services/storefront';
import { expect } from '@woo/fixtures/BaseTest';
import wooPlugin from '@woo/WooPlugin';
import { OrderApiService } from '@woo/services/api';

export default class Przelewy24PaymentMethod extends PaymentMethod {
    /**
     * Custom order status verification that handles both 'pending' and 'processing' statuses
     */
    async onAdminVerifyOrderStatus(): Promise<void> {
        // Get the AdminOrder service instance that will be used later
        const adminOrderService = (this.processFlowService as any).adminOrderService;

        // Get order data using the same method the AdminOrder service uses
        const orderData = await new OrderApiService().getOrderStatus(wooPlugin.sharedData['orderNumber']);

        // Accept both 'pending' and 'processing' as valid statuses for Przelewy24
        expect(['pending', 'processing']).toContain(orderData.status);
        expect(orderData.amounts?.total).toEqual(wooPlugin.sharedData['orderSummary']['totals']['total']);

        // Set the orderData on the AdminOrder service instance so it can be used later
        if (adminOrderService) {
            adminOrderService.orderData = orderData;
        }

        console.log(`Przelewy24: Order status verified as '${orderData.status}'`);
    }
    /**
     * Executes the steps to handle unsuccessful order placements.
     */
    async afterNegativePlaceOrder(): Promise<void> {
        this.processFlowService.addDisabledStep(OrderSteps.AdminVerifyOrderStatus);
        this.processFlowService.addDisabledStep(OrderSteps.BuckarooTransactionVerify);
        this.processFlowService.addDisabledStep(OrderSteps.SuccessPage);

        await this.submitStatus(this.options.responseStatus);

        await expect(wooPlugin.page).toHaveURL(/\/checkout\/\?bck_err=.+/);
        console.log('Order failed!');
    }

    /**
     * Handles actions after placing the order.
     */
    async afterPlaceOrder(): Promise<void> {
        console.log('Submitting status...');
        // Use the specific responseStatus if provided
        // Otherwise default to 190 (Success)
        await this.submitStatus(this.options.responseStatus);
    }

    /**
     * Submits the payment status.
     * Przelewy24 status codes: 190 (Success), 490 (Failed), 690 (Rejected), 890 (Cancelled by user)
     */
    private async submitStatus(responseStatus: string = '190'): Promise<void> {
        await wooPlugin.page.selectOption(wooPlugin.getSelector('storefront.checkout.selectStatus'), responseStatus);
        await wooPlugin.page.click(wooPlugin.getSelector('storefront.checkout.submitStatus'));
    }
}
